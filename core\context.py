from importlib import import_module
from types import ModuleType
from .setting import SETTINGS
from .utility import load_json, save_json
class ContextManager:   
    """上下文类"""
    def __init__(self) -> None:
        self._cache = {}
    
    def module_obj(self,  key: str, module_name: str=None, *args, **kwargs):
        """获取对象"""
        obj = self._cache.get(key, None)
        if obj:
            return obj
        if not module_name:
            print(f"关键字 {key}, 请先初始化.")
            return
        try:
            module: ModuleType = import_module(module_name)
            obj = module.Object(*args, **kwargs)
        except ModuleNotFoundError:
            print(f"找不到类库驱动{module_name}")
            return
        self._cache[key] = obj
        return obj

    def init_default(self):
        """初始化默认对象"""
        self.module_obj(key=f"geteway", module_name=f"vnpy_xt")
    

context_manager = ContextManager()






