"""
Basic data structure used for general trading function in the trading platform.
"""

from dataclasses import dataclass, field
from datetime import datetime as Datetime

@dataclass
class StockData:
    __slots__ = [
        'vt_symbol', "name", 'float_volume',  'total_volume',  'float_volume',
        'limit_ratio', 'limit_up', 'limit_down', 'pre_close', "pre_transaction_num",
        "pre_volume", "pre_amount", "stop_status",
        "start_stop_time",
        
    ]
    start_stop_time: list[Datetime] = []
    stop_status: int = 0 # 0: 1:涨停 -1：跌停  2: 卖一价等于涨停价(涨停前)


@dataclass
class TickData:
    #   stop_status: int = 0 # 0: 1:涨停 -1：跌停  2: 卖一价等于涨停价(涨停前)
    __slots__ = [
        'vt_symbol', 'datetime',  'volume', 'amount', 'open_interest',
        'last_price', 'open', 'high', 'low', 'pre_close', "transaction_num",
        'bid_price_1', 'bid_price_2', 'bid_price_3', 'bid_price_4', 'bid_price_5',
        'ask_price_1', 'ask_price_2', 'ask_price_3', 'ask_price_4', 'ask_price_5',
        'bid_volume_1', 'bid_volume_2', 'bid_volume_3', 'bid_volume_4', 'bid_volume_5',
        'ask_volume_1', 'ask_volume_2', 'ask_volume_3', 'ask_volume_4', 'ask_volume_5',
        'net_volume', 'net_amount', "index", "stop_status", "timestamp"
        'max_bid_volume_1', 'max_ask_volume_1', 'max_bid_price_1', 'max_ask_price_1',
        'yang_volume', 'yin_volume', 'yang_amount', 'yin_amount', 'localtime'
    ]
    vt_symbol: str
    datetime: Datetime = None
    timestamp: int = 0
    volume: float = 0
    amount: float = 0
    open_interest: float = 0
    last_price: float = 0


    open: float = 0
    high: float = 0
    low: float = 0
    pre_close: float = 0
    transaction_num: int = 0
    volRatio: float = 0
    speed1Min: float = 0
    speed5Min: float = 0

    bid_price_1: float = 0
    bid_price_2: float = 0
    bid_price_3: float = 0
    bid_price_4: float = 0
    bid_price_5: float = 0

    ask_price_1: float = 0
    ask_price_2: float = 0
    ask_price_3: float = 0
    ask_price_4: float = 0
    ask_price_5: float = 0

    bid_volume_1: float = 0
    bid_volume_2: float = 0
    bid_volume_3: float = 0
    bid_volume_4: float = 0
    bid_volume_5: float = 0

    ask_volume_1: float = 0
    ask_volume_2: float = 0
    ask_volume_3: float = 0
    ask_volume_4: float = 0
    ask_volume_5: float = 0

    net_volume: float = 0
    net_amount: float = 0
    stop_status: int = 0
    max_bid_volume_1: float = 0
    max_ask_volume_1: float = 0
    max_bid_price_1: float = 0
    max_ask_price_1: float = 0
    yang_volume: float = 0
    yin_volume: float = 0
    yang_amount: float = 0
    yin_amount: float = 0
    index: int = -1
 
