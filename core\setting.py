"""
Global setting of the trading platform.
"""

from logging import <PERSON><PERSON><PERSON><PERSON>
from tzlocal import get_localzone_name

from .utility import load_json


SETTINGS: dict = {
    "font.family": "微软雅黑",
    "font.size": 12,

    "log.active": True,
    "log.level": CRITIC<PERSON>,
    "log.console": True,
    "log.file": True,

    "email.server": "smtp.qq.com",
    "email.port": 465,
    "email.username": "",
    "email.password": "",
    "email.sender": "",
    "email.receiver": "",

    "datafeed.name": "",
    "datafeed.username": "",
    "datafeed.password": "",

    "gateway.name": "xt",
    "gateway.token": "",
    

    "database.timezone": get_localzone_name(),
    "database.name": "taos",
    # "database.database": "test_10",
    "database.database": "real_data_03",
    "database.host": "**************",
    "database.user": "root",
    "database.password": "taosdata",
    "database.port": 6041,

}


# Load global setting from json file.
SETTING_FILENAME: str = "vt_setting.json"
# SETTINGS.update(load_json(SETTING_FILENAME))
