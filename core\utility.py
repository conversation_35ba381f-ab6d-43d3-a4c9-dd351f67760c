"""
General utility functions.
"""

import json
import time
import datetime
# from datetime import datetime, time
from pathlib import Path
from collections.abc import Callable
from decimal import Decimal
from math import floor, ceil

import numpy as np
# import talib
from zoneinfo import ZoneInfo, available_timezones      # noqa

from .object import  TickData

def time_string_to_hms_int(time_str):
    dt = datetime.datetime.strptime(time_str, "%H:%M:%S")
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def timestamp_to_hms_int(timestamp):
    dt = datetime.datetime.fromtimestamp(timestamp)
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def datatime_to_hms_int(dt: datetime.datetime):
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def generate_datetime(timestamp: int, millisecond: bool = True) -> datetime.datetime:
        """生成本地时间"""
        if millisecond:
            dt: datetime.datetime = datetime.datetime.fromtimestamp(timestamp / 1000)
        else:
            dt = datetime.datetime.fromtimestamp(timestamp)
        return dt

def generate_timestamp(datetime: datetime.datetime, millisecond: bool = True) -> int:
        """生成本地时间"""
        timestamp = datetime.timestamp()
        if millisecond:
            timestamp = int(timestamp * 1000)
        else:
            timestamp = int(timestamp * 1000)
        return timestamp


def load_json(filepath: str) -> dict:
    """
    Load data from json file in temp path.
    """
    if filepath.exists():
        with open(filepath, encoding="UTF-8") as f:
            data: dict = json.load(f)
        return data



def save_json(filepath: str, data: dict) -> None:
    """
    Save data into json file in temp path.
    """
    with open(filepath, mode="w+", encoding="UTF-8") as f:
        json.dump(
            data,
            f,
            indent=4,
            ensure_ascii=False
        )


