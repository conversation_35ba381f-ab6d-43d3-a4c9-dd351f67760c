from core.context import context_manager
from core.object import TickData
from core.cash_data import CashManager
from core.utility import datatime_to_hms_int
import time
import datetime
import traceback
from threading import Thread
from queue import Queue, Empty
import logging
logger = logging.getLogger(__name__)

class BaseStrategy:
    def __init__(self) -> None:
        self.queue: Queue = Queue()
        self.name = "base_strategy"
        self.active = False
       
        self.geteway_api = context_manager.module_obj("geteway")
        self.thread_lst: list[Thread] = [
            Thread(target=self.on_tick),
        ]
        self.n = 1
        self.tick_dct = {}
        self.pre_day_data = {}
        self.cash = CashManager()
        # self.full_tick = self.cash_tick.full_tick
        # self.stock_info = self.cash_tick.stock_info
        # self.all_ticks = self.cash_tick.all_ticks

    def f01(self, vt_symbol: str):
        tick: TickData = self.cash.vt_symbols[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3)*tick.bid_price_1 / 10000
        return val
    
    def f01b(self, vt_symbol: str):
        tick: TickData = self.cash.vt_symbols[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) * \
        ((tick.bid_price_1 + tick.bid_price_2 + tick.bid_price_3) / 3) / 10000
        return val
    
    def f02(self, vt_symbol: str):
        tick: TickData = self.cash.vt_symbols[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) * tick.bid_price_1 / 10000
        return val
    
    def f03(self, vt_symbol: str):
        tick: TickData = self.cash.vt_symbols[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) / (tick.bid_volume_1 + tick.bid_volume_2)
        return val
    
    def f04(self, vt_symbol: str):
        tick: TickData = self.cash.vt_symbols[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.bid_volume_1 + tick.bid_volume_2)
        return val
    
    def f05(self, vt_symbol: str):
        cash_tick = self.cash.vt_symbols[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) / cash_tick.float_volume *100
        return val

    def f06(self,  vt_symbol: str):
        cash_tick = self.cash.vt_symbols[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2) / tick.ask_volume_1
        return val
    
    def f06b(self, vt_symbol: str):
        cash_tick = self.cash.vt_symbols[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2) / (tick.bid_volume_1)
        return val
    
    def f07(self, vt_symbol: str):
        cash_tick = self.cash.vt_symbols[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)
        return val
    
    def f08(self, vt_symbol: str):
        cash_tick = self.cash.vt_symbols[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)
        return         val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)

    
    def f08(self, tick: TickData):
        fti = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)
        return fti
    
    def f09(self, tick: TickData):
        fti = tick.net_volume / tick.float_volume *100
        return fti
    def f10(self, tick: TickData):
        fti = tick.volume / tick.float_volume *100
        return fti
    def f11(self, tick: TickData):
        fti = tick.net_amount
        return fti
    
    def f12(self, tick: TickData):
        fti = tick.net_amount
        return fti
    
    def f13(self, tick: TickData):
        fti = tick.bid_volume_1 / tick.float_volume *100
        return fti
    def f13b(self, tick: TickData):
        fti = tick.max_bid_volume_1 / tick.float_volume *100
        return fti
    
    def f14(self, tick: TickData):
        fti = tick.ask_volume_1 / tick.float_volume *100
        return fti
    def f14b(self, tick: TickData):
        fti = tick.max_ask_volume_1 / tick.float_volume *100
        return fti
    def f15(self, tick: TickData):
        fti = tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5
        return fti
    def f15b(self, tick: TickData):
        fti = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / tick.float_volume *100
        return fti
    
    def f16a(self, vt_symbol: str, start_time: str, end_time: str):
        fti = 0
        ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.ask_price_1:
                fti += tick.net_volume
            pre_tick = tick
        return fti
    
    def f16c(self,vt_symbol: str, start_time: str, end_time: str, n=1):
        fti = 0
        ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > getattr(pre_tick, f"ask_volume_{n}"):
                fti += tick.net_volume
                pre_tick = tick
        return fti
    
    def f1b(self,vt_symbol: str, start_time: str, end_time: str):
        fti = 0
        ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.bid_price_1:
                fti += tick.net_volume
            pre_tick = tick
        return fti
    
    def f16d(self, vt_symbol, start_time: str, end_time: str, n=1):
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < getattr(pre_tick, f"bid_volume_{n}"):
                fti += tick.net_volume
                pre_tick = tick
        return fti
    def f17(self, vt_symbol, start_time: str, end_time: str):
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                fti += tick.net_amount
            pre_tick = tick
        return round(fti/10000, 2)
    
    def f18(self, vt_symbol, start_time: str, end_time: str):
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                fti += tick.net_amount
            pre_tick = tick
        return round(fti/10000, 2)
    
    def f19(self, vt_symbol, start_time: str, end_time: str):
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                fti += tick.net_volume
            pre_tick = tick
        return fti
    
    def f19a(self, vt_symbol, start_time: str, end_time: str):
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price == pre_tick.last_price:
                fti += tick.net_volume
            pre_tick = tick
        return fti
    def f20(self, vt_symbol, start_time: str, end_time: str, n: float):
        # n = 2 代表2%
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
               if tick.net_volume / self.stock_info[vt_symbol].float_volume * 100 > n: 
                    fti += tick.net_volume
            pre_tick = tick
        return fti
    def f20b(self, vt_symbol, start_time: str, end_time: str, n: float):
        # n = 2 代表2%
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
               if tick.net_volume / self.stock_info[vt_symbol].float_volume * 100 > n: 
                    fti += tick.net_volume
            pre_tick = tick
        return fti
    def f21(self, vt_symbol, start_time: str, end_time: str, n: float):
        # 金额 单位万
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
               if tick.net_amount / 10000 > n: 
                    fti += tick.net_amount
            pre_tick = tick
        return fti
    
    def f21b(self, vt_symbol, start_time: str, end_time: str, n: float):
        # 金额 单位万
        fti = 0
        ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
               if tick.net_amount / 10000 > n: 
                    fti += tick.net_amount
            pre_tick = tick
        return fti
    def f22(self, vt_symbol, start_time: str, end_time: str, ticks: list[TickData]=None):
        # 金额 单位万
        fti = 0
        if not ticks:
            ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        tmp_lst = []
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                tmp_lst.append(tick.net_volume)
            pre_tick = tick
        fti = max(tmp_lst) / self.stock_info[vt_symbol].float_volume * 100
        return fti
    
    def f22b(self, vt_symbol, start_time: str, end_time: str, ticks: list[TickData]=None):
        # 金额 单位万
        fti = 0
        if not ticks:
            ticks = ticks = self.cash_tick.query_ticks(vt_symbol, start_time, end_time)
        if not ticks:
            return fti
        tmp_lst = []
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                tmp_lst.append(tick.net_amount)
            pre_tick = tick
        fti = max(tmp_lst) / 10000
        return fti
    
    def f23(self, ticks: list[TickData], n: int):
        fti = 0
        if not ticks:
            return fti
        tmp_lst = [tick.transaction_num for tick in ticks if tick.net_volume < n]
        fti = sum(tmp_lst)
        return fti
    def f24(self, ticks: list[TickData], n: int):
        fti = 0
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                fti += tick.transaction_num
            pre_tick = tick
        return fti
  
    def f25(self, ticks: list[TickData], n: int):
        fti = 0
        if not ticks:
            return fti
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                fti += tick.transaction_num
            pre_tick = tick
        return fti
    
    def f26(self, ticks: TickData):
        return ticks.transaction_num

    def f27(self, tick:TickData):
        fti = self.stock_info[tick.vt_symbol].pre_transaction_num
        return fti
    def f28(self, tick:TickData):
        fti = self.stock_info[tick.vt_symbol].pre_volume
        return fti



    def f31(self, tick: TickData):
        fti = tick.bid_volume_1 / self.stock_info[tick.vt_symbol].float_volume *100
        return fti
    
    def f32(self, tick: TickData, n: int):
        vt_symbol_ticks = self.cash_tick.query_ticks_n(tick.vt_symbol, n-1)
        vt_symbol_ticks.append(tick)
        fti = min([t.bid_volume_1 for t in vt_symbol_ticks]) / self.stock_info[tick.vt_symbol].float_volume *100
        return fti
    
    def f33(self, tick: TickData, n: int):
        vt_symbol_ticks = self.cash_tick.query_ticks_n(tick.vt_symbol, n-1)
        vt_symbol_ticks.append(tick)
        fti = max([t.bid_volume_1 for t in vt_symbol_ticks]) / self.stock_info[tick.vt_symbol].float_volume *100
        return fti
    
    def f34(self, tick: TickData, n: int):
        vt_symbol_ticks = self.cash_tick.query_ticks_n(tick.vt_symbol, n-1)
        vt_symbol_ticks.append(tick)
        fti = (min([t.bid_volume_1 for t in vt_symbol_ticks]) / tick.open - 1 ) *100
        return fti
    
    def f35(self, tick: TickData, n: int):
        vt_symbol_ticks = self.cash_tick.query_ticks_n(tick.vt_symbol, n-1)
        vt_symbol_ticks.append(tick)
        fti = (max([t.bid_volume_1 for t in vt_symbol_ticks]) / tick.open - 1 ) *100
        return fti
    
    def f36(self, tick: TickData):
        fti = (tick.bid_price_1 / tick.low - 1 )*100
        return fti
    def f37(self, tick: TickData):
        fti = (tick.bid_price_1 / tick.high - 1 )*100
        return fti
    
    def f37b(self, tick: TickData, time: str):
        old_tick: TickData = self.cash_tick.query_tick(tick.vt_symbol, time, is_before=False)
        fti = (old_tick.bid_price_1 / old_tick.high - 1 )*100
        return fti
    
    def f37c(self, tick: TickData, start_time="09:30:00", end_time=None):
        if not end_time:
            end_time = tick.datetime.strftime("%H:%M:%S")
        ticks = self.cash_tick.query_ticks(tick.vt_symbol, start_time, end_time)
        fti = (max([t.bid_price_1 for t in ticks]) / tick.open - 1) *100
        return fti
    
    def f38(self, tick: TickData):
        fti = (tick.open / tick.low - 1 )*100
        return fti
    def f39(self, tick: TickData):
        fti = tick.amount / tick.volume
        return fti
    def f40(self, tick: TickData):
        fti = tick.yang_amount / tick.yang_volume
        return fti
    def f41(self, tick: TickData):
        fti = tick.yin_amount / tick.yin_volume
        return fti
    
    def f42(self, tick: TickData):
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return None
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t.stop_status == 1:
                return time_str
        return None
    
    def f43(self, tick: TickData):
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return None
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t.stop_status == 2:
                return time_str
        return None
    
    def f44(self, tick: TickData):
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return None
        up_stop_cnt = 0
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t.stop_status == 1:
                up_stop_cnt += 1
            if up_stop_cnt == 1 and t.stop_status == 2:
                return time_str
        return None
    
    def f45(self, tick: TickData):
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return None
        up_stop_cnt = 0
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t.stop_status == 1:
                up_stop_cnt += 1
            if up_stop_cnt == 2:
                return time_str
        return None
   
    def f45b(self, tick: TickData, n : int=1):
        time_str = self.f45(tick)
        if not time_str:
            return 0
        ticks = self.cash_tick.query_ticks_by_endtime_n(tick.vt_symbol, time_str, n)
        fti = sum([t.bid_price_1 for t in ticks])
        return fti

    def f45b(self, tick: TickData, n : int=1):
        time_str = self.f45(tick)
        if not time_str:
            return 0
        ticks = self.cash_tick.query_ticks_by_endtime_n(tick.vt_symbol, time_str, n)
        fti = sum([t.net_volume for t in ticks])
        return fti
    
    def f46(self, tick: TickData):
        time_str = self.f45(tick)
        if not time_str:
            return 0
        tick = self.cash_tick.query_tick(tick.vt_symbol, time_str)
        fti = tick.low
        return fti
    
    def f47(self, tick: TickData):
        time_str = self.f45(tick)
        if not time_str:
            return 0
        t_tick = self.cash_tick.query_tick(tick.vt_symbol, time_str)
        index = t_tick.index
        ticks = self.cash_tick.query_ticks_by_index(t_tick.vt_symbol, start_index=index, end_index=-1)
        fti = min([t.last_price for t in ticks])
        return fti
    
    def f47b(self, tick: TickData):
        time_str = self.f45(tick)
        if not time_str:
            return 0
        t_tick = self.cash_tick.query_tick(tick.vt_symbol, time_str)
        low = self.f47(tick)
        fti = (low / t_tick.low)  * 100
        return fti
    def f48a(self, tick: TickData):
        fti = 0
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return fti
        up_stop_time = []
        start_time = None
        end_time = None
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t_tick = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t_tick.stop_status== 1:
                start_time = time_str
            elif start_time and t_tick.stop_status != 1:
                end_time = time_str
                up_stop_time.append((start_time, end_time))
                start_time = None
                end_time = None
        if start_time:
            up_stop_time.append((start_time, time_str))
        for start, end in up_stop_time:
            st_tick = self.cash_tick.query_tick(tick.vt_symbol, start)
            end_tick = self.cash_tick.query_tick(tick.vt_symbol, end)
            fti += end_tick.volume - st_tick.volume
        return fti
    
    def f48b(self, tick: TickData):
        fti = self.f48a(tick) / self.stock_info[tick.vt_symbol].float_volume * 100
        return fti
    
    def f48c(self, tick: TickData):
        # 涨停封单率 s所有还是首次
        pass
    def f48d(self, tick: TickData):
        fti = 0
        start_stop_time = self.stock_info[tick.vt_symbol].start_stop_time
        if not start_stop_time:
            return fti
        up_stop_time = []
        start_time = None
        end_time = None
        for dt in start_stop_time:
            time_str = datatime_to_hms_int(dt)
            t_tick = self.cash_tick.query_tick(tick.vt_symbol, time_str)
            if t_tick.stop_status== 1:
                start_time = time_str
            elif start_time and t_tick.stop_status != 1:
                end_time = time_str
                up_stop_time.append((start_time, end_time))
                start_time = None
                end_time = None
        if start_time:
            up_stop_time.append((start_time, time_str))
        for start, end in up_stop_time:
            ticks = self.cash_tick.query_ticks(tick.vt_symbol, start, end)
            fti += sum([t.bid_volume_1 for t in ticks])
        return fti
    def f48e(self, tick: TickData):
        idx = tick.index
        pre_tick = self.cash_tick.query_ticks_by_index(tick.vt_symbol, idx-1, idx)[0]
        fti = (tick.bid_volume_1 - pre_tick.bid_volume_1 - tick.net_volume) / self.stock_info[tick.vt_symbol].float_volume * 100
        return fti
    
    def f49(self, tick: TickData):
        t

    def on_tick(self, ticks: list[TickData]) -> None:
        while self.active:
            try:
                ticks: list[TickData] = self.queue.get(timeout=1)
                for tick in ticks:

                    
                    self.cash_tick.update_tick(tick)

            except Empty:
                continue
            except Exception:
                self.active = False
                info: str = traceback.format_exc()
                print(f"触发异常，录制已停止：\n{info}")
                logger.error(f"触发异常，录制已停止：\n{info}")


    def start(self) -> None:
        self.geteway_api.add_queue(self.queue, key=self.name)





# db_clinet = context_manager.module_obj(key="db_client")
# db_clinet.drop_db("test_09")
