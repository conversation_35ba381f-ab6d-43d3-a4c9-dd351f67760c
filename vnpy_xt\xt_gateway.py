from datetime import datetime
from collections.abc import Callable
import logging
from threading import Thread
from typing import Any
from queue import Empty, Queue
import time
from xtquant import (
    xtdata,
    xtdatacenter as xtdc
)
from xtquant import xtconstant
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import (
    StockAccount,
    XtAsset,
    XtOrder,
    XtPosition,
    XtTrade,
    XtOrderResponse,
    XtCancelOrderResponse,
    XtOrderError,
    XtCancelError
)
from .xt_config import VIP_ADDRESS_LIST, LISTEN_PORT
from core.constant import (
    Exchange,
)
from core.object import TickData, ContractData



logger = logging.getLogger(__name__)
# 全局缓存字典
symbol_contract_map: dict[str, dict] = {}       # 合约数据

class XTGateway:
    def __init__(self) -> None:
        self.md_api = XtMdApi()
        self.td_api = XtTdApi()
        self.add_queue = self.md_api.add_queue
        self.remove_queue = self.md_api.remove_queue
        self.symbol_contract_map = symbol_contract_map
    
    def subscribe(self) -> None:
        self.md_api.subscribe_whole_quote()

    def connect(self, setting: dict={}) -> None:
        self.md_api.connect(setting.get("token"))
        # self.td_api.connect(setting["path"], setting["accountid"], setting["account_type"])

    def close(self) -> None:
        self.md_api.close()
        self.td_api.close()

class XtMdApi:
    """行情API"""

    def __init__(self) -> None:
        """构造函数"""
        self.inited: bool = False
        self.subscribed: bool = False
        self.token: str = ""
        self.queue_dct: dict[str, Queue] = {}
        self.full_tick = {}
        self.symbol_contract_map = symbol_contract_map

    def add_queue(self, queue: Queue, key: str):
        if key in self.queue_dct:
            return
        self.queue_dct[key] = queue

    def remove_queue(self, key: str):
        if key in self.queue_dct:
            del self.queue_dct[key]

    def onMarketData(self, data: dict) -> None:
        """行情推送回调"""
        ticks = []
        for vt_symbol, d in data.items():
            time_stamp = d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            dt = datetime.fromtimestamp(time_stamp / 1000)
            tick: TickData = TickData(
                vt_symbol=vt_symbol,
                datetime=dt,
                volume=d["volume"],
                amount=d["amount"],
                open_interest=d["openInt"],
                last_price=round(d["lastPrice"], 3),
                open=round(d["open"], 3),
                high=round(d["high"], 3),
                low=round(d["low"], 3),
                pre_close=round(d["lastClose"], 3),
                transaction_num=d["transactionNum"],
                volRatio=d["volRatio"],
                speed1Min=d["speed1Min"],
                speed5Min=d["speed5Min"],
                bid_price_1=round(bp_data[0], 3),
                bid_price_2=round(bp_data[1], 3),
                bid_price_3=round(bp_data[2], 3),
                bid_price_4=round(bp_data[3], 3),
                bid_price_5=round(bp_data[4], 3),
                ask_price_1=round(ap_data[0], 3),
                ask_price_2=round(ap_data[1], 3),
                ask_price_3=round(ap_data[2], 3),
                ask_price_4=round(ap_data[3], 3),
                ask_price_5=round(ap_data[4], 3),
                bid_volume_1=bv_data[0],
                bid_volume_2=bv_data[1],
                bid_volume_3=bv_data[2],
                bid_volume_4=bv_data[3],
                bid_volume_5=bv_data[4],
                ask_volume_1=av_data[0],
                ask_volume_2=av_data[1],
                ask_volume_3=av_data[2],
                ask_volume_4=av_data[3],
                ask_volume_5=av_data[4],
            )
            ticks.append(tick)

        for queue in self.queue_dct.values():
            queue.put(ticks)
    
    def connect(
        self,
        token: str,
    ) -> None:
        """连接"""

        self.token = token

        if self.inited:
            logger.info("行情接口已经初始化，请勿重复操作")
            return
        try:
            self.init_xtdc()
            # 尝试查询合约信息，确认连接成功
            xtdata.get_instrument_detail("000001.SZ")
        except Exception as ex:
            logger.error(f"迅投研数据服务初始化失败，发生异常：{ex}")
            return

        self.inited = True

        logger.info("行情接口连接成功")
        self.query_contracts()


    def init_xtdc(self) -> None:
        """初始化xtdc服务进程"""
        if self.inited or not self.token:
            return

        # 设置token
        xtdc.set_token(self.token)

        # 设置连接池
        xtdc.set_allow_optmize_address(VIP_ADDRESS_LIST)

        # 开启使用期货真实夜盘时间
        xtdc.set_future_realtime_mode(True)

        # 执行初始化，但不启动默认58609端口监听
        xtdc.init(False)

        # 设置监听端口
        xtdc.listen(port=LISTEN_PORT)

    def query_contracts(self) -> None:
        """查询合约信息"""
        self.query_stock_contracts()

    def query_stock_contracts(self) -> None:
        """查询股票合约信息"""
        markets: list = [
            "沪深A股",
            "沪深转债",
            "沪深ETF",
            "沪深指数",
            "京市A股"
        ]

        for i in markets:
            xt_symbols: list = xtdata.get_stock_list_in_sector(i)
            product = ""
            if i == "沪深A股":
                product = "股票"
            elif i == "沪深转债":
                product = "可转债"
            elif i == "沪深ETF":
                product = "ETF"
            elif i == "沪深指数":
                product = "指数"
            elif i == "京市A股":
                product = "股票"
            for xt_symbol in xt_symbols:
                symbol, market = xt_symbol.split(".")
                # 生成并推送合约信息
                data: dict = xtdata.get_instrument_detail(xt_symbol)
                self.symbol_contract_map[xt_symbol] = ContractData(
                    symbol=symbol,
                    exchange=market,
                    name=data["InstrumentName"],
                    product=product,
                    pricetick=data["PriceTick"],
                    min_volume=data["VolumeMultiple"],
                    limit_up=data["UpStopPrice"],
                    limit_down=data["DownStopPrice"],
                    float_volume=data["FloatVolume"],
                    total_volume=data["TotalVolume"],
                    pre_close=data["PreClose"],

                )

    
    def subscribe_whole_quote(self):
        if self.subscribed:
            return
        xt_symbols: list[str] = []
        markets: list = [
            "沪深A股",
            "沪深转债",
            "沪深ETF",
            "沪深指数",
            "京市A股"
        ]

        for i in markets:
            names: list = xtdata.get_stock_list_in_sector(i)
            xt_symbols.extend(names)
        xtdata.subscribe_whole_quote(code_list=xt_symbols,callback=self.onMarketData)
        self.subscribed = True


    def close(self) -> None:
        """关闭连接"""
        pass


class XtTdApi(XtQuantTraderCallback):
    """交易API"""

    def __init__(self):
        """构造函数"""
        super().__init__()

        self.inited: bool = False
        self.connected: bool = False

        self.account_id: str = ""
        self.path: str = ""
        self.account_type: str = ""

        self.order_count: int = 0

        self.active_localid_sysid_map: dict[str, str] = {}

        self.xt_client: XtQuantTrader = None
        self.xt_account: StockAccount = None

    def on_connected(self) -> None:
        """
        连接成功推送
        """
        logger.info("交易接口连接成功")

    def on_disconnected(self) -> None:
        """连接断开"""
        logger.info("交易接口连接断开，请检查与客户端的连接状态")
        self.connected = False

        # 尝试重连，重连需要更换session_id
        session: int = int(float(datetime.now().strftime("%H%M%S.%f")) * 1000)
        connect_result: int = self.connect(self.path, self.accountid, self.account_type, session)

        if connect_result:
            logger.error("交易接口重连失败")
        else:
            logger.info("交易接口重连成功")
            
    def close(self) -> None:
        """关闭连接"""
        pass
